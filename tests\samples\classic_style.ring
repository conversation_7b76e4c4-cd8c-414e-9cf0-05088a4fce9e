# Classic Ring Style Example (Style 1)
# Uses: see/give, func...ok, if...ok


# Simple output
see "Hello World!" + nl
? "This is classic Ring style"

# Input and variables
see "Enter your name: "
give cName
see "Hello " + cName + nl

# Function call
nSum = calculateSum(10, 20)
see "Sum: " + nSum + nl

# Conditional statements
if nSum > 25
    see "Sum is greater than 25" + nl
but nSum = 25
    see "Sum equals 25" + nl
else
    see "Sum is less than 25" + nl
ok

# Loop example
for i = 1 to 5
    see "Number: " + i + nl
next

# While loop
nCounter = 1
while nCounter <= 3
    see "Counter: " + nCounter + nl
    nCounter++
next

# Switch statement
nValue = 2
on nValue
    on 1
        see "Value is one" + nl
    on 2
        see "Value is two" + nl
    other
        see "Value is something else" + nl
off
# Create object and use it
oPerson = new Person("Ahmed", 25)
oPerson.introduce()

# Array example
aNumbers = [1, 2, 3, 4, 5]
for nNum in aNumbers
    see "Array item: " + nNum + nl
next

# List operations
aList = []
add(aList, "Apple")
add(aList, "Banana")
add(aList, "Orange")

for cFruit in aList
    see "Fruit: " + cFruit + nl
next

see "Classic Ring style example completed!" + nl

# Function definition
func calculateSum nX, nY
    nResult = nX + nY
    return nResult

# Class definition
class Person
    cName = ""
    nAge = 0
    
    func init cPersonName, nPersonAge
        cName = cPersonName
        nAge = nPersonAge
    
    
    func introduce
        see "My name is " + cName + " and I am " + nAge + " years old" + nl
    
    
    private
    
    func privateMethod
        see "This is a private method" + nl
    

