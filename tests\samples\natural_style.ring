# Natural Modified Ring Style Example (Style 2)
# Uses: put/get, def...end, if...end

# Simple output
put "Hello World!"
put "This is natural modified Ring style"

# Input and variables
put "Enter your name: "
get cName
put "Hello " + cName

# Function call
nSum = calculateSum(10, 20)
put "Sum: " + nSum

# Conditional statements
if nSum > 25
    put "Sum is greater than 25"
elseif nSum = 25
    put "Sum equals 25"
else
    put "Sum is less than 25"
end

# Loop example
for i = 1 to 5
    put "Number: " + i
end

# While loop
nCounter = 1
while nCounter <= 3
    put "Counter: " + nCounter
    nCounter++
end

# Create object and use it
oPerson = new Person("Fatima", 30)
oPerson.introduce()

# Array example
aNumbers = [1, 2, 3, 4, 5]
for nNum in aNumbers
    put "Array item: " + nNum
end

# List operations
aList = []
add(aList, "Apple")
add(aList, "Banana")
add(aList, "Orange")

for cFruit in aList
    put "Fruit: " + cFruit
end

put "Natural modified Ring style example completed!"

# Switch statement
nValue = 2
case nValue
    case 1
        put "Value is one"
    case 2
        put "Value is two"
    other
        put "Value is something else"
end

# Error handling (supported in natural style)
try
    nResult = 10 / 0
catch
    put "Error occurred: " + cCatchError
end


# Function definition
def calculateSum nX, nY
    nResult = nX + nY
    return nResult
end

# Class definition
class Person
    cName = ""
    nAge = 0
    
    def init cPersonName, nPersonAge
        cName = cPersonName
        nAge = nPersonAge
    end
    
    def introduce
        put "My name is " + cName + " and I am " + nAge + " years old"
    end
    
    private
    
    def privateMethod
        put "This is a private method"
    end
end

