/*
**	RingFmtCLI Class
**	Command Line Interface for RingFmt
**	<PERSON>les command line arguments and user interaction
*/

class RingFmtCLI

	# Properties
	cInputFile = ""
	cOutputFile = ""
	cStyle = "style1"
	lBackup = false
	lVerbose = false
	lShowHelp = false
	lShowVersion = false
	aInputFiles = []
	
	# RingFmt instance
	oRingFmt
	
	func init
		oRingFmt = new RingFmt
		
	func processArgs aArgs
		# Process command line arguments
		if len(aArgs) < 3 {
			displayHelp()
			return
		}
		
		# Parse arguments
		for i = 3 to len(aArgs) {
			cArg = aArgs[i]
			
			if cArg = "-h" or cArg = "--help"{
				lShowHelp = true
			elseif cArg = "-v" or cArg = "--version"
				lShowVersion = true
			elseif cArg = "-backup" or cArg = "--backup"
				lBackup = true
			elseif cArg = "-verbose" or cArg = "--verbose"
				lVerbose = true
			elseif cArg = "-style1" or cArg = "--style1" or cArg = "-classic" or cArg = "--classic"
				cStyle = "style1"
			elseif cArg = "-style2" or cArg = "--style2" or cArg = "-natural" or cArg = "--natural"
				cStyle = "style2"
			elseif cArg = "-style3" or cArg = "--style3" or cArg = "-modern" or cArg = "--modern"
				cStyle = "style3"
			else 
				if left(cArg, 1) = "-" {
					if left(cArg, 8) = "-output=" {
						cOutputFile = substr(cArg, 9)
					elseif left(cArg, 9) = "--output=" 
						cOutputFile = substr(cArg, 10)
					else
						? "Unknown option: " + cArg
						displayHelp()
						return
					}
				else
					# Input file
					add(aInputFiles, cArg)
				}
			}
		}
		
		# Handle special flags
		if lShowHelp {
			displayHelp()
			return
		}
		
		if lShowVersion {
			showVersion()
			return
		}
		
		# Validate input files
		if len(aInputFiles) = 0 {
			? "Error: No input files specified"
			displayHelp()
			return
		}
		
		# Set options
		oRingFmt.setBackup(lBackup)
		oRingFmt.setVerbose(lVerbose)
		
		# Process files
		processFiles()
		
	func processFiles
		# Process all input files
		for cFile in aInputFiles {
			processFile(cFile)
		}
		
	func processFile cFile
		# Process single file
		try {
			# Handle wildcards
			if substr(cFile, "*") > 0 {
				processWildcard(cFile)
				return
			}
			
			# Determine output file
			cOutput = cOutputFile
			if cOutput = "" {
				cOutput = cFile
			}
			
			if lVerbose {
				? "Processing: " + cFile + " -> " + cOutput
				? "Language Style: " + getLanguageStyleName(cStyle)
			}
			
			# Format the file
			oRingFmt.formatFile(cFile, cOutput, cStyle)
			
			if not lVerbose {
				? "Formatted: " + cFile
			}
			
		catch
			? "Error processing " + cFile + ": " + cCatchError
		}
		
	func processWildcard cPattern
		# Process files matching wildcard pattern
		aFiles = dir(cPattern)
		for aFile in aFiles {
			if aFile[2] = false {  # Not a directory
				cFileName = aFile[1]
				if right(lower(cFileName), 5) = ".ring" {
					processFile(cFileName)
				}
			}
		}
		
	func getLanguageStyleName cStyleName
		# Get descriptive name for language style
		switch cStyleName {
		case "style1"
			return "Classic Ring (see/give, func...ok)"
		case "style2"
			return "Natural Modified (put/get, def...end)"
		case "style3"
			return "Modern C-like (print(), func{})"
		other
			return cStyleName
		}
		
	func displayHelp
		# Display help information
		? "RingFmt - Ring Code Formatter with Language Style Conversion"
		? ""
		? "Usage: ringfmt [options] <input_files>"
		? ""
		? "Language Styles:"
		? "  -style1, --style1, -classic, --classic"
		? "    Classic Ring style (see/give, func...ok, if...ok)"
		? "  -style2, --style2, -natural, --natural"
		? "    Natural Modified style (put/get, def...end, if...end)"
		? "  -style3, --style3, -modern, --modern"
		? "    Modern C-like style (print(), func{}, if(){})"
		? ""
		? "Options:"
		? "  -backup, --backup         Create backup files before formatting"
		? "  -verbose, --verbose       Show detailed processing information"
		? "  -output=<file>, --output=<file>  Specify output file"
		? "  -h, --help               Show this help message"
		? "  -v, --version            Show version information"
		? ""
		? "Examples:"
		? "  ringfmt myfile.ring                    # Format with classic style"
		? "  ringfmt myfile.ring -style2           # Convert to natural style"
		? "  ringfmt myfile.ring -modern -backup   # Convert to modern with backup"
		? "  ringfmt *.ring -style3 -verbose       # Convert all files to modern"
		? ""
		? "Language Style Conversion:"
		? "  Classic:  see 'Hello'  give x  func test  return x  ok"
		? "  Natural:  put 'Hello'  get x   def test   return x  end"
		? "  Modern:   print('Hello') getstring() func test { return x }"
		
	func showVersion
		# Display version information
		? "RingFmt - Ring Code Formatter"
		? "Version: 1.1.0"
		? "Features: Language Style Conversion, Code Formatting"
		? "Supported Styles: Classic, Natural Modified, Modern C-like"
		? "Author: Ring Community"
