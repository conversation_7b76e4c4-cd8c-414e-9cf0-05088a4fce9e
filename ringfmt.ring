/*
**	RingFmt - Ring Code Formatter
**	Main library file
**	Author: Ring Community
*/

# Load required components
load "src/tokenparser.ring"
load "src/formatter.ring" 
load "src/styles.ring"
load "src/cli.ring"

/*
**	Main RingFmt Class
**	Coordinates the formatting process
*/
class RingFmt

	# Properties
	cInputFile = ""
	cOutputFile = ""
	cStyle = "style1"
	lBackup = false
	lVerbose = false
	
	# Components
	oTokenParser
	oFormatter
	oStyle
	
	func init
		# Initialize components
		oTokenParser = new TokenParser
		oFormatter = new CodeFormatter
		oStyle = new FormattingStyles
		
	func formatFile cFile, cOutputFile, cStyleName
		# Main formatting function
		if lVerbose {
			? "Formatting file: " + cFile
			? "Output file: " + cOutputFile  
			? "Style: " + cStyleName
		}
		
		# Validate input file
		if not fexists(cFile) {
			raise("Input file does not exist: " + cFile)
		}
		
		# Create backup if requested
		if lBackup {
			createBackup(cFile)
		}
		
		# Parse tokens from file
		if lVerbose {
			? "Parsing tokens..."
		}
		oTokenParser.parseFile(cFile)
		aTokens = oTokenParser.getTokens()
		
		# Set formatting style
		oFormatter.setStyle(oStyle.getStyle(cStyleName))
		
		# Format the code
		if lVerbose {
			? "Formatting code..."
		}
		cFormattedCode = oFormatter.format(aTokens)
		
		# Write output
		if lVerbose {
			? "Writing output..."
		}
		write(cOutputFile, cFormattedCode)
		
		if lVerbose {
			? "Formatting completed successfully!"
		}
		
		return true
		
	func formatString cCode, cStyleName
		# Format code from string
		oTokenParser.parseString(cCode)
		aTokens = oTokenParser.getTokens()
		
		oFormatter.setStyle(oStyle.getStyle(cStyleName))
		return oFormatter.format(aTokens)
		
	func setBackup lValue
		lBackup = lValue
		
	func setVerbose lValue
		lVerbose = lValue
		
	private
	
		func createBackup cFile
			# Create backup file
			cBackupFile = cFile + ".backup"
			if lVerbose {
				? "Creating backup: " + cBackupFile
			}
			copy(cFile, cBackupFile)
