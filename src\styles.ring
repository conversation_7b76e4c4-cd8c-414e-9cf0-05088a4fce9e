/*
**	FormattingStyles Class
**	Defines different formatting styles for Ring code
**	Based on Ring language documentation styles
*/

/*
**	LanguageStyleConverter Class
**	Converts Ring code between different language styles
**	Style 1: Classic (see/give, func...ok, if...ok)
**	Style 2: Natural Modified (put/get, def...end, if...end)
**	Style 3: Modern C-like (print()/getstring(), func{}, if(){})
*/
class LanguageStyleConverter

	# Style definitions with keyword mappings
	aStyleMappings = []

	func init
		initializeStyleMappings()

	func convertToStyle cCode, cTargetStyle
		# Convert code to target language style
		cTargetStyle = lower(cTargetStyle)

		if cTargetStyle = "style1" or cTargetStyle = "classic" {
			return convertToClassic(cCode)
		elseif cTargetStyle = "style2" or cTargetStyle = "natural"
			return convertToNatural(cCode)
		elseif cTargetStyle = "style3" or cTargetStyle = "modern"
			return convertToModern(cCode)
		else 
			raise("Unknown language style: " + cTargetStyle)
		}

	private

		func initializeStyleMappings
			# Initialize keyword mappings between styles
			aStyleMappings = [
				# Output functions
				[:classic = "see", :natural = "put", :modern = "print()"],
				[:classic = "?", :natural = "put", :modern = "puts()"],

				# Input functions
				[:classic = "give", :natural = "get", :modern = "getstring()"],

				# Function definition
				[:classic = "func", :natural = "def", :modern = "func"],

				# Function end
				[:classic = "", :natural = "end", :modern = "}"],

				# Conditional statements
				[:classic = "if", :natural = "if", :modern = "if"],
				[:classic = "but", :natural = "elseif", :modern = "elseif"],
				[:classic = "else", :natural = "else", :modern = "else"],

				# Conditional end
				[:classic = "ok", :natural = "end", :modern = "}"],

				# Loop statements
				[:classic = "for", :natural = "for", :modern = "for"],
				[:classic = "while", :natural = "while", :modern = "while"],
				[:classic = "next", :natural = "end", :modern = "}"],

				# Switch statements
				[:classic = "on", :natural = "case", :modern = "case:"],
				[:classic = "other", :natural = "other", :modern = "default:"],
				[:classic = "off", :natural = "end", :modern = "}"],

				# Class definition
				[:classic = "class", :natural = "class", :modern = "class"],
				[:classic = "ok", :natural = "end", :modern = "}"],

				# Error handling
				[:classic = "", :natural = "try", :modern = "try"],
				[:classic = "", :natural = "catch", :modern = "catch"],
				[:classic = "", :natural = "end", :modern = "}"]
			]

		func convertToClassic cCode
			# Convert to classic Ring style (Style 1)
			cResult = cCode

			# Convert output functions
			cResult = substr(cResult, "put ", "see ")
			cResult = substr(cResult, "print(", "see ")
			cResult = substr(cResult, "puts(", "? ")

			# Convert input functions
			cResult = substr(cResult, "get ", "give ")
			cResult = substr(cResult, "getstring()", "give ")

			# Convert function definitions
			cResult = substr(cResult, "def ", "func ")

			# Convert block endings
			cResult = substr(cResult, " end", " ok")
			cResult = substr(cResult, "}", "ok")

			# Convert conditionals
			cResult = substr(cResult, "elseif", "but")

			# Convert loops
			cResult = substr(cResult, " end", " next")

			# Convert switch statements
			cResult = substr(cResult, "case ", "on ")
			cResult = substr(cResult, "default:", "other")
			cResult = substr(cResult, "off", "off")

			return cResult

		func convertToNatural cCode
			# Convert to natural modified Ring style (Style 2)
			cResult = cCode

			# Convert output functions
			cResult = substr(cResult, "see ", "put ")
			cResult = substr(cResult, "? ", "put ")
			cResult = substr(cResult, "print(", "put ")
			cResult = substr(cResult, "puts(", "put ")

			# Convert input functions
			cResult = substr(cResult, "give ", "get ")
			cResult = substr(cResult, "getstring()", "get ")

			# Convert function definitions
			cResult = substr(cResult, "func ", "def ")

			# Convert block endings
			cResult = substr(cResult, " ok", " end")
			cResult = substr(cResult, "}", " end")

			# Convert conditionals
			cResult = substr(cResult, "but", "elseif")

			# Convert loops
			cResult = substr(cResult, " next", " end")

			# Convert switch statements
			cResult = substr(cResult, "on ", "case ")
			cResult = substr(cResult, "other", "other")
			cResult = substr(cResult, "off", "end")

			# Add error handling support
			# (Natural style supports try/catch/end)

			return cResult

		func convertToModern cCode
			# Convert to modern C-like Ring style (Style 3)
			cResult = cCode

			# Convert output functions
			cResult = substr(cResult, "see ", "print(")
			cResult = substr(cResult, "? ", "puts(")
			cResult = substr(cResult, "put ", "print(")

			# Convert input functions
			cResult = substr(cResult, "give ", "getstring()")
			cResult = substr(cResult, "get ", "getstring()")

			# Convert function definitions - keep func but add braces
			# This requires more sophisticated parsing

			# Convert block endings
			cResult = substr(cResult, " ok", " }")
			cResult = substr(cResult, " end", " }")
			cResult = substr(cResult, " next", " }")

			# Convert conditionals - add parentheses and braces
			# This requires more sophisticated parsing for proper syntax

			# Convert switch statements
			cResult = substr(cResult, "on ", "case ")
			cResult = substr(cResult, "other", "default")
			cResult = substr(cResult, "off", "}")

			# Add error handling with braces
			cResult = substr(cResult, "try", "try {")
			cResult = substr(cResult, "catch", "catch")
			cResult = substr(cResult, "end", "}")
			return cResult

class FormattingStyles

	# Available styles
	aStyles = []
	
	func init
		# Initialize all formatting styles
		initializeStyles()
		
	func getStyle cStyleName
		# Get style configuration by name
		cStyleName = lower(cStyleName)
		
		for oStyle in aStyles {
			if lower(oStyle[:name]) = cStyleName {
				return oStyle
			}
		}
		
		# Return default style if not found
		return getDefaultStyle()
		
	func getAvailableStyles
		# Get list of available style names
		aStyleNames = []
		for oStyle in aStyles {
			add(aStyleNames, oStyle[:name])
		}
		return aStyleNames
		
	func getDefaultStyle
		# Return the default style (style1)
		return getStyle("style1")
		
	private
	
		func initializeStyles
			# Initialize all three formatting styles

			# Style 1 - Classic Ring style (see/give, func...ok)
			oStyle1 = [
				:name = "style1",
				:description = "Classic Ring style (see/give, func...ok)",
				:languageStyle = "classic",
				:indentString = "    ",  # 4 spaces
				:spaceBeforeBrace = true,
				:spaceAfterBrace = false,
				:spaceAroundOperators = true,
				:spaceAroundAssignment = true,
				:spaceAfterComma = true,
				:spaceAfterKeyword = true,
				:newlineAfterBrace = true,
				:newlineBeforeCloseBrace = true,
				:maxLineLength = 80,
				:alignParameters = false,
				:compactArrays = false,
				:compactObjects = false,
				# Language-specific keywords
				:outputKeyword = "see",
				:outputShortKeyword = "?",
				:inputKeyword = "give",
				:functionKeyword = "func",
				:functionEndKeyword = "ok",
				:conditionalElseKeyword = "but",
				:blockEndKeyword = "ok",
				:loopEndKeyword = "next",
				:switchOnKeyword = "on",
				:switchOtherKeyword = "other",
				:switchEndKeyword = "off"
			]
			
			# Style 2 - Natural Modified Ring style (put/get, def...end)
			oStyle2 = [
				:name = "style2",
				:description = "Natural Modified Ring style (put/get, def...end)",
				:languageStyle = "natural",
				:indentString = "\t",    # Tab character
				:spaceBeforeBrace = false,
				:spaceAfterBrace = false,
				:spaceAroundOperators = true,
				:spaceAroundAssignment = true,
				:spaceAfterComma = true,
				:spaceAfterKeyword = true,
				:newlineAfterBrace = true,
				:newlineBeforeCloseBrace = true,
				:maxLineLength = 100,
				:alignParameters = true,
				:compactArrays = false,
				:compactObjects = false,
				# Language-specific keywords
				:outputKeyword = "put",
				:outputShortKeyword = "put",
				:inputKeyword = "get",
				:functionKeyword = "def",
				:functionEndKeyword = "end",
				:conditionalElseKeyword = "elseif",
				:blockEndKeyword = "end",
				:loopEndKeyword = "end",
				:switchOnKeyword = "case",
				:switchOtherKeyword = "other",
				:switchEndKeyword = "end",
				:tryKeyword = "try",
				:catchKeyword = "catch",
				:errorEndKeyword = "end"
			]
			
			# Style 3 - Modern C-like Ring style (print(), func{})
			oStyle3 = [
				:name = "style3",
				:description = "Modern C-like Ring style (print(), func{})",
				:languageStyle = "modern",
				:indentString = "  ",    # 2 spaces
				:spaceBeforeBrace = false,
				:spaceAfterBrace = false,
				:spaceAroundOperators = true,
				:spaceAroundAssignment = true,
				:spaceAfterComma = true,
				:spaceAfterKeyword = false,
				:newlineAfterBrace = true,
				:newlineBeforeCloseBrace = true,
				:maxLineLength = 120,
				:alignParameters = false,
				:compactArrays = true,
				:compactObjects = true,
				# Language-specific keywords
				:outputKeyword = "print",
				:outputShortKeyword = "puts",
				:inputKeyword = "getstring",
				:functionKeyword = "func",
				:functionEndKeyword = "}",
				:conditionalElseKeyword = "elseif",
				:blockEndKeyword = "}",
				:loopEndKeyword = "}",
				:switchOnKeyword = "case:",
				:switchOtherKeyword = "default:",
				:switchEndKeyword = "}",
				:tryKeyword = "try{}",
				:catchKeyword = "catch",
				:errorEndKeyword = "}",
				:useBraces = true,
				:useParentheses = true
			]
			
			# Add styles to collection
			aStyles = [oStyle1, oStyle2, oStyle3]
			
		func validateStyle oStyle
			# Validate style configuration
			aRequiredKeys = [
				"name", "description", "indentString",
				"spaceBeforeBrace", "spaceAfterBrace",
				"spaceAroundOperators", "spaceAroundAssignment",
				"spaceAfterComma", "spaceAfterKeyword",
				"newlineAfterBrace", "newlineBeforeCloseBrace",
				"maxLineLength"
			]
			
			for cKey in aRequiredKeys {
				if not find(oStyle, cKey) {
					raise("Style missing required key: " + cKey)
				}
			}
			
			return true
