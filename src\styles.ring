/*
**	FormattingStyles Class
**	Defines different formatting styles for Ring code
**	Based on Ring language documentation styles
*/

class FormattingStyles

	# Available styles
	aStyles = []
	
	func init
		# Initialize all formatting styles
		initializeStyles()
		
	func getStyle cStyleName
		# Get style configuration by name
		cStyleName = lower(cStyleName)
		
		for oStyle in aStyles {
			if lower(oStyle[:name]) = cStyleName {
				return oStyle
			}
		}
		
		# Return default style if not found
		return getDefaultStyle()
		
	func getAvailableStyles
		# Get list of available style names
		aStyleNames = []
		for oStyle in aStyles {
			add(aStyleNames, oStyle[:name])
		}
		return aStyleNames
		
	func getDefaultStyle
		# Return the default style (style1)
		return getStyle("style1")
		
	private
	
		func initializeStyles
			# Initialize all three formatting styles
			
			# Style 1 - Standard Ring documentation style
			oStyle1 = [
				:name = "style1",
				:description = "Standard Ring documentation style",
				:indentString = "    ",  # 4 spaces
				:spaceBeforeBrace = true,
				:spaceAfterBrace = false,
				:spaceAroundOperators = true,
				:spaceAroundAssignment = true,
				:spaceAfterComma = true,
				:spaceAfterKeyword = true,
				:newlineAfterBrace = true,
				:newlineBeforeCloseBrace = true,
				:maxLineLength = 80,
				:alignParameters = false,
				:compactArrays = false,
				:compactObjects = false
			]
			
			# Style 2 - Alternative Ring documentation style  
			oStyle2 = [
				:name = "style2",
				:description = "Alternative Ring documentation style",
				:indentString = "\t",    # Tab character
				:spaceBeforeBrace = false,
				:spaceAfterBrace = false,
				:spaceAroundOperators = true,
				:spaceAroundAssignment = true,
				:spaceAfterComma = true,
				:spaceAfterKeyword = true,
				:newlineAfterBrace = true,
				:newlineBeforeCloseBrace = true,
				:maxLineLength = 100,
				:alignParameters = true,
				:compactArrays = false,
				:compactObjects = false
			]
			
			# Style 3 - Compact Ring documentation style
			oStyle3 = [
				:name = "style3", 
				:description = "Compact Ring documentation style",
				:indentString = "  ",    # 2 spaces
				:spaceBeforeBrace = false,
				:spaceAfterBrace = false,
				:spaceAroundOperators = false,
				:spaceAroundAssignment = false,
				:spaceAfterComma = false,
				:spaceAfterKeyword = false,
				:newlineAfterBrace = false,
				:newlineBeforeCloseBrace = false,
				:maxLineLength = 120,
				:alignParameters = false,
				:compactArrays = true,
				:compactObjects = true
			]
			
			# Add styles to collection
			aStyles = [oStyle1, oStyle2, oStyle3]
			
		func validateStyle oStyle
			# Validate style configuration
			aRequiredKeys = [
				"name", "description", "indentString",
				"spaceBeforeBrace", "spaceAfterBrace",
				"spaceAroundOperators", "spaceAroundAssignment",
				"spaceAfterComma", "spaceAfterKeyword",
				"newlineAfterBrace", "newlineBeforeCloseBrace",
				"maxLineLength"
			]
			
			for cKey in aRequiredKeys {
				if not find(oStyle, cKey) {
					raise("Style missing required key: " + cKey)
				}
			}
			
			return true
