# Comprehensive Grammar Test for Ring Language Styles
# Tests all major grammar constructs in Classic Ring style

# Package statement (if supported)
# package MyPackage

# Import statements
# import System.IO

# Load statements
load "stdlib.ring"

# Output statements - 'see'|'put' <Expr>
see "=== Ring Language Style Test ===" + nl
? "Testing Classic Ring Style"

# Input statements - 'give'|'get' <Identifier>
see "Enter your name: "
give cUserName
see "Hello " + cUserName + nl

# Variable assignments
nAge = 25
cCity = "Cairo"
lIsActive = true

# Function definitions - 'func'|'def'|'function' <Identifier> [ParaList]
func simpleFunction
    return "Simple function result"
ok

func functionWithParams cName, nValue
    return "Name: " + cName + ", Value: " + nValue
ok

func functionWithOptionalParams cName, nValue, lFlag
    if lFlag = null
        lFlag = false
    ok
    return cName + " - " + nValue + " - " + lFlag
ok

# Function calls
cResult = simpleFunction()
see "Function result: " + cResult + nl

cResult2 = functionWithParams("Test", "100")
see "Function with params: " + cResult2 + nl

# Conditional statements - 'if' <Expr> {'but'|'elseif'} ['else'] 'ok'|'end'|'}'|'endif'
if nAge >= 18
    see "Adult" + nl
but nAge >= 13
    see "Teenager" + nl
else
    see "Child" + nl
ok

# Nested conditionals
if cCity = "Cairo"
    if nAge > 20
        see "Adult in Cairo" + nl
    else
        see "Young person in Cairo" + nl
    ok
else
    see "Not in Cairo" + nl
ok

# Switch statements - 'Switch' <Expr> {'on'|'case'} ['other'] 'off'|'end'|'}'|'endswitch'
nDayOfWeek = 3
on nDayOfWeek
    on 1
        see "Monday" + nl
    on 2
        see "Tuesday" + nl
    on 3
        see "Wednesday" + nl
    on 4
        see "Thursday" + nl
    on 5
        see "Friday" + nl
    other
        see "Weekend" + nl
off

# For loops - 'for' <Identifier> '=' <Expr> 'to' <Expr> ['step' <Expr>]
see "Counting 1 to 5:" + nl
for i = 1 to 5
    see "Count: " + i + nl
next

see "Counting 2 to 10 by 2:" + nl
for i = 2 to 10 step 2
    see "Even: " + i + nl
next

# For-in loops - 'for'|'foreach' <Identifier> 'in' <Expr>
aFruits = ["Apple", "Banana", "Orange", "Grape"]
see "Fruits list:" + nl
for cFruit in aFruits
    see "- " + cFruit + nl
next

# While loops - 'while' <Expr>
nCounter = 1
see "While loop:" + nl
while nCounter <= 3
    see "Counter: " + nCounter + nl
    nCounter++
next

# Do-while loops - 'do' {statement} 'again' <Expr>
nValue = 1
see "Do-while loop:" + nl
do
    see "Value: " + nValue + nl
    nValue++
again nValue <= 3

# Class definitions - 'class' <Identifier> ['from'|':'|'<' <Identifier>]
class Person
    # Properties
    cName = ""
    nAge = 0
    cEmail = ""
    
    # Constructor
    func init cPersonName, nPersonAge
        cName = cPersonName
        nAge = nPersonAge
    ok
    
    # Public methods
    func introduce
        see "My name is " + cName + " and I am " + nAge + " years old" + nl
    ok
    
    func setEmail cNewEmail
        cEmail = cNewEmail
    ok
    
    func getInfo
        return "Name: " + cName + ", Age: " + nAge + ", Email: " + cEmail
    ok
    
    # Private section
    private
    
    func validateAge nAge
        return nAge >= 0 and nAge <= 150
    ok
ok

# Class inheritance
class Employee from Person
    cJobTitle = ""
    nSalary = 0
    
    func init cName, nAge, cTitle, nSal
        # Call parent constructor
        super.init(cName, nAge)
        cJobTitle = cTitle
        nSalary = nSal
    ok
    
    func introduce
        see "I am " + cName + ", " + cJobTitle + " (" + nAge + " years old)" + nl
    ok
    
    func getJobInfo
        return cJobTitle + " - Salary: " + nSalary
    ok
ok

# Object creation and usage
oPerson = new Person("Ahmed", 30)
oPerson.introduce()
oPerson.setEmail("<EMAIL>")
see oPerson.getInfo() + nl

oEmployee = new Employee("Fatima", 28, "Developer", 5000)
oEmployee.introduce()
see oEmployee.getJobInfo() + nl

# Arrays and lists
aNumbers = [1, 2, 3, 4, 5]
aNames = ["Ali", "Sara", "Omar"]
aMixed = [1, "Hello", true, 3.14]

see "Numbers array:" + nl
for nNum in aNumbers
    see "Number: " + nNum + nl
next

# List operations
aList = []
add(aList, "First")
add(aList, "Second")
add(aList, "Third")

see "Dynamic list:" + nl
for cItem in aList
    see "Item: " + cItem + nl
next

# Hash/Dictionary operations
oHash = [
    :name = "John",
    :age = 35,
    :city = "New York"
]

see "Hash contents:" + nl
see "Name: " + oHash[:name] + nl
see "Age: " + oHash[:age] + nl
see "City: " + oHash[:city] + nl

# Anonymous functions
fSquare = func nX
    return nX * nX
ok

nResult = call fSquare(5)
see "Square of 5: " + nResult + nl

# Control flow statements
for i = 1 to 10
    if i = 5
        see "Skipping 5" + nl
        loop  # continue
    ok
    if i = 8
        see "Breaking at 8" + nl
        exit  # break
    ok
    see "Processing: " + i + nl
next

# Return statement
func testReturn nValue
    if nValue < 0
        return "Negative"
    ok
    if nValue = 0
        return "Zero"
    ok
    return "Positive"
ok

see "Test return: " + testReturn(-5) + nl
see "Test return: " + testReturn(0) + nl
see "Test return: " + testReturn(10) + nl

# Complex expressions
nA = 10
nB = 20
nC = 30

# Arithmetic
nSum = nA + nB + nC
nProduct = nA * nB
nDivision = nB / nA

see "Sum: " + nSum + nl
see "Product: " + nProduct + nl
see "Division: " + nDivision + nl

# Logical operations
lResult1 = (nA > 5) and (nB < 25)
lResult2 = (nA = 10) or (nB = 30)
lResult3 = not (nC < 20)

see "Logical results: " + lResult1 + ", " + lResult2 + ", " + lResult3 + nl

# Comparison operations
lGreater = nB > nA
lEqual = nA = 10
lNotEqual = nB != nC

see "Comparisons: " + lGreater + ", " + lEqual + ", " + lNotEqual + nl

see "=== Test Completed ===" + nl
