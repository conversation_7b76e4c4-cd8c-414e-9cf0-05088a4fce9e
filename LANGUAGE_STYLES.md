# Ring Language Styles Guide

This document describes the three different language styles supported by RingFmt and how they map to Ring's grammar.

## Ring Language Grammar Overview

Based on the official Ring grammar, the language supports multiple syntax variations for the same constructs:

### Statement Types

1. **Output Statements**: `'see'|'put' <Expr>`
2. **Input Statements**: `'give'|'get' <Identifier>`
3. **Function Definitions**: `'func'|'def'|'function' <Identifier> [ParaList] ['{' {statement} '}']['end'|'endfunc'|'endfunction']`
4. **Conditionals**: `'if' <Expr> ['{'] {statement} [ {'but'|'elseif' <Expr> {Statement} } ] ['else' {Statement} ] 'ok'|'end'|'}'|'endif'`
5. **Switch Statements**: `'Switch' <Expr> ['{'] { 'on'|'case' <Expr> {statement} } ['other' {Statement} ] 'off'|'end'|'}'|'endswitch'`
6. **Loops**: Various forms with different ending keywords
7. **Try-Catch**: `'try' {statement} ['{'] 'catch' {statement} 'done'|'end'|'}'|'endtry'`

## Language Style Mappings

### Style 1: Classic Ring (Default)

**Philosophy**: Traditional Ring syntax with simple keywords and `ok` endings.

| Construct | Syntax | Example |
|-----------|--------|---------|
| Output | `see <expr>` or `? <expr>` | `see "Hello World!" + nl` |
| Input | `give <identifier>` | `give cName` |
| Function | `func name ... ok` | `func test return "OK" ok` |
| Conditional | `if ... but ... else ... ok` | `if x > 5 see "Big" but x = 5 see "Equal" else see "Small" ok` |
| Loop | `for ... next` | `for i = 1 to 10 see i next` |
| Switch | `on ... other ... off` | `on x on 1 see "One" other see "Other" off` |
| Class | `class ... ok` | `class Person ... ok` |
| Try-Catch | Not supported | Use conditional checks instead |

**Characteristics**:
- Simple, readable keywords
- Consistent `ok` endings for most blocks
- `but` instead of `elseif`
- `next` for loop endings
- No built-in error handling

### Style 2: Natural Modified

**Philosophy**: More natural language-like syntax with `end` endings.

| Construct | Syntax | Example |
|-----------|--------|---------|
| Output | `put <expr>` | `put "Hello World!"` |
| Input | `get <identifier>` | `get cName` |
| Function | `def name ... end` | `def test return "OK" end` |
| Conditional | `if ... elseif ... else ... end` | `if x > 5 put "Big" elseif x = 5 put "Equal" else put "Small" end` |
| Loop | `for ... end` | `for i = 1 to 10 put i end` |
| Switch | `case ... other ... end` | `case x case 1 put "One" other put "Other" end` |
| Class | `class ... end` | `class Person ... end` |
| Try-Catch | `try ... catch ... end` | `try risky_operation() catch put "Error: " + cCatchError end` |

**Characteristics**:
- Natural language keywords (`put`, `get`)
- Consistent `end` endings
- Standard `elseif`
- Full error handling support
- More verbose but clearer

### Style 3: Modern C-like

**Philosophy**: C/JavaScript-like syntax with braces and parentheses.

| Construct | Syntax | Example |
|-----------|--------|---------|
| Output | `print(<expr>)` or `puts(<expr>)` | `print("Hello World!\n")` |
| Input | `getstring()` | `cName = getstring()` |
| Function | `func name() { ... }` | `func test() { return "OK" }` |
| Conditional | `if (...) { ... } elseif { ... } else { ... }` | `if (x > 5) { print("Big") } elseif (x == 5) { print("Equal") } else { print("Small") }` |
| Loop | `for (...) { ... }` | `for (i = 1; i <= 10; i++) { print(i) }` |
| Switch | `switch (...) { case: ... default: ... }` | `switch (x) { case: 1 print("One") default: print("Other") }` |
| Class | `class ... { ... }` | `class Person { ... }` |
| Try-Catch | `try { ... } catch { ... }` | `try { risky_operation() } catch { print("Error: " + cCatchError) }` |

**Characteristics**:
- Function-style I/O (`print()`, `getstring()`)
- Braces for all blocks
- Parentheses for conditions
- C-style loop syntax support
- String interpolation with `#{variable}`
- Modern error handling

## Grammar-Based Conversion Rules

### Block Endings Conversion

| Classic | Natural | Modern | Context |
|---------|---------|--------|---------|
| `ok` | `end` | `}` | Functions, Classes, Conditionals |
| `next` | `end` | `}` | Loops |
| `off` | `end` | `}` | Switch statements |
| N/A | `end` | `}` | Try-catch blocks |

### Keyword Mappings

| Classic | Natural | Modern | Purpose |
|---------|---------|--------|---------|
| `see` | `put` | `print()` | Output |
| `?` | `put` | `puts()` | Short output |
| `give` | `get` | `getstring()` | Input |
| `func` | `def` | `func` | Function definition |
| `but` | `elseif` | `elseif` | Conditional alternative |
| `on` | `case` | `case:` | Switch case |
| `other` | `other` | `default:` | Switch default |

### Special Constructs

#### Error Handling
- **Classic**: Not supported (use conditional checks)
- **Natural**: `try ... catch ... end`
- **Modern**: `try { ... } catch { ... }`

#### String Interpolation
- **Classic**: String concatenation with `+`
- **Natural**: String concatenation with `+`
- **Modern**: `#{variable}` syntax supported

#### Control Flow
- **Classic**: `exit`, `loop`
- **Natural**: `exit`, `loop`
- **Modern**: `break`, `continue`

## Usage Examples

### Converting Between Styles

```bash
# Convert classic to natural
ringfmt classic_code.ring -natural -output natural_code.ring

# Convert natural to modern
ringfmt natural_code.ring -modern -output modern_code.ring

# Convert modern back to classic
ringfmt modern_code.ring -classic -output classic_code.ring
```

### Complete Example

**Classic Style:**
```ring
see "Enter number: "
give nNum
func factorial nN
    if nN <= 1
        return 1
    else
        return nN * factorial(nN - 1)
    ok
ok
nResult = factorial(nNum)
see "Factorial: " + nResult + nl
```

**Natural Style:**
```ring
put "Enter number: "
get nNum
def factorial nN
    if nN <= 1
        return 1
    else
        return nN * factorial(nN - 1)
    end
end
nResult = factorial(nNum)
put "Factorial: " + nResult
```

**Modern Style:**
```ring
print("Enter number: ")
nNum = getstring()
func factorial(nN) {
    if (nN <= 1) {
        return 1
    } else {
        return nN * factorial(nN - 1)
    }
}
nResult = factorial(nNum)
print("Factorial: #{nResult}\n")
```

## Best Practices

1. **Choose style based on team preference and project requirements**
2. **Be consistent within a project**
3. **Use Classic for traditional Ring development**
4. **Use Natural for more readable, English-like code**
5. **Use Modern for developers familiar with C/JavaScript syntax**
6. **Always test converted code before deployment**
7. **Use version control when converting existing codebases**
