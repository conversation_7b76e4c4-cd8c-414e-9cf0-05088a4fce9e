aPackageInfo = [
	:name = "ringfmt",
	:description = "Ring Code Formatter - A tool to format Ring programming language source code",
	:folder = "ringfmt",
	:developer = "Ring Community",
	:email = "",
	:license = "MIT License",
	:version = "1.0.0",
	:ringversion = "1.18",
	:versions = 	[
		[
			:version = "1.0.0",
			:branch = "master"
		]
	],
	:libs = 	[
		[
			:name = "tokenslib",
			:version = "",
			:providerusername = ""
		]
	],
	:files = 	[
		"main.ring",
		"ringfmt.ring",
		"src/tokenparser.ring",
		"src/formatter.ring",
		"src/styles.ring",
		"src/cli.ring",
		"tests/test_formatter.ring",
		"tests/samples/test1.ring",
		"tests/samples/test2.ring",
		"README.md"
	],
	:ringfolderfiles = 	[
		"bin/ringfmt.ring"
	],
	:windowsfiles = 	[

	],
	:linuxfiles = 	[

	],
	:macosfiles = 	[

	],
	:windowsringfolderfiles = 	[

	],
	:linuxringfolderfiles = 	[

	],
	:macosringfolderfiles = 	[

	],
	:run = "ring main.ring",
	:setup = "",
	:windowssetup = "",
	:linuxsetup = "",
	:macossetup = "",
	:ubuntusetup = "",
	:fedorasetup = "",
	:remove = "",
	:windowsremove = "",
	:linuxremove = "",
	:macosremove = "",
	:ubunturemove = "",
	:fedoraremove = ""
]
