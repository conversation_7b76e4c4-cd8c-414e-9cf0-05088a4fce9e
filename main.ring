#!/usr/bin/env ring

/*
**	RingFmt - Ring Code Formatter
**	Author: Ring Community
**	Description: A tool to format Ring programming language source code
**	Version: 1.0.0
*/

# Load required libraries
load "ringfmt.ring"

func main
	# Create CLI handler and process command line arguments
	oCLI = new RingFmtCLI
	oCLI.processArgs(sysargv)

# Entry point when run directly
if isMainFile() {
	main()
}

func isMainFile
	# Check if this file is being run directly
	cCurrentFile = filename()
	cMainFile = sysargv[2]
	if cMainFile = NULL {
		return false
	}
	return (lower(cCurrentFile) = lower(cMainFile))
