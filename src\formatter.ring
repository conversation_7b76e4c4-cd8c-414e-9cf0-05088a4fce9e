/*
**	CodeFormatter Class
**	Formats Ring source code according to specified style
**	Handles indentation, spacing, and line breaks
*/

class CodeFormatter

	# Properties
	oStyle = null
	oLanguageConverter = null
	nIndentLevel = 0
	cIndentString = "    "  # Default: 4 spaces
	cOutput = ""
	lInClass = false
	lInFunction = false
	lInLoop = false
	lInCondition = false
	
	# Token type constants
	C_KEYWORD 	= 0
	C_OPERATOR 	= 1
	C_LITERAL 	= 2
	C_NUMBER 	= 3
	C_IDENTIFIER 	= 4
	C_ENDLINE 	= 5
	C_COMMENT	= 6
	
	func setStyle oStyleObj
		oStyle = oStyleObj
		if oStyle != null {
			cIndentString = oStyle[:indentString]
			# Initialize language converter if needed
			if oLanguageConverter = null {
				oLanguageConverter = new LanguageStyleConverter
			}
		}
		
	func format aTokens
		# Main formatting function
		if oStyle = null {
			raise("No formatting style set")
		}
		
		# Reset state
		nIndentLevel = 0
		cOutput = ""
		lInClass = false
		lInFunction = false
		lInLoop = false
		lInCondition = false
		
		# Process each token
		for i = 1 to len(aTokens) {
			oToken = aTokens[i]
			processToken(oToken, i, aTokens)
		}
		
		return cOutput
		
	private
	
		func processToken oToken, nIndex, aTokens
			# Process individual token based on type and context
			
			switch oToken[:type] {
			case C_KEYWORD
				processKeyword(oToken, nIndex, aTokens)
			case C_OPERATOR
				processOperator(oToken, nIndex, aTokens)
			case C_LITERAL
				processLiteral(oToken, nIndex, aTokens)
			case C_NUMBER
				processNumber(oToken, nIndex, aTokens)
			case C_IDENTIFIER
				processIdentifier(oToken, nIndex, aTokens)
			case C_ENDLINE
				processEndLine(oToken, nIndex, aTokens)
			case C_COMMENT
				processComment(oToken, nIndex, aTokens)
			}
			
		func processKeyword oToken, nIndex, aTokens
			# Process keyword tokens with language style conversion
			cKeyword = upper(oToken[:value])
			cConvertedKeyword = convertKeyword(cKeyword)

			# Create converted token
			oConvertedToken = [
				:type = oToken[:type],
				:value = cConvertedKeyword,
				:line = oToken[:line],
				:column = oToken[:column]
			]

			# Handle indentation changes before keyword
			switch cKeyword {
			case "CLASS"
				addIndentedToken(oConvertedToken)
				lInClass = true
			case "FUNC" case "FUNCTION" case "DEF"
				addIndentedToken(oConvertedToken)
				lInFunction = true
			case "IF"
				addIndentedToken(oConvertedToken)
				lInCondition = true
			case "FOR" case "WHILE" case "FOREACH"
				addIndentedToken(oConvertedToken)
				lInLoop = true
			case "ELSE" case "ELSEIF" case "BUT"
				# Decrease indent for else/elseif
				nIndentLevel--
				addIndentedToken(oConvertedToken)
				nIndentLevel++
			case "ENDCLASS"
				nIndentLevel--
				addIndentedToken(oConvertedToken)
				lInClass = false
			case "ENDFUNC" case "ENDFUNCTION" case "OK" case "END"
				# Handle different function end keywords
				if lInFunction {
					nIndentLevel--
					addIndentedToken(oConvertedToken)
					lInFunction = false
				else
					addToken(oConvertedToken)
				}
			case "ENDIF" case "OK" case "END"
				# Handle different conditional end keywords
				if lInCondition {
					nIndentLevel--
					addIndentedToken(oConvertedToken)
					lInCondition = false
				else
					addToken(oConvertedToken)
				}
			case "ENDFOR" case "ENDWHILE" case "NEXT" case "END"
				# Handle different loop end keywords
				if lInLoop {
					nIndentLevel--
					addIndentedToken(oConvertedToken)
					lInLoop = false
				else
					addToken(oConvertedToken)
				}
			case "PRIVATE"
				# Special handling for private section
				nIndentLevel--
				addIndentedToken(oConvertedToken)
				nIndentLevel++
			case "SEE" case "PUT" case "PRINT"
				# Handle output keywords
				addToken(oConvertedToken)
			case "GIVE" case "GET" case "GETSTRING"
				# Handle input keywords
				addToken(oConvertedToken)
			other
				addToken(oConvertedToken)
			}
			
		func processOperator oToken, nIndex, aTokens
			# Process operator tokens with proper spacing
			cOperator = oToken[:value]
			
			switch cOperator {
			case "{"
				# Opening brace - add space before and increase indent
				if oStyle[:spaceBeforeBrace] {
					cOutput += " "
				}
				cOutput += cOperator
				nIndentLevel++
			case "}"
				# Closing brace - decrease indent and add on new line
				nIndentLevel--
				if not isLastCharNewline() {
					cOutput += nl
				}
				cOutput += getCurrentIndent() + cOperator
			case "="
				# Assignment operator - add spaces around
				if oStyle[:spaceAroundAssignment] {
					cOutput += " " + cOperator + " "
				else
					cOutput += cOperator
				}
			case "+" case "-" case "*" case "/" case "%"
				# Arithmetic operators - add spaces around
				if oStyle[:spaceAroundOperators] {
					cOutput += " " + cOperator + " "
				else
					cOutput += cOperator
				}
			case "(" case ")"
				# Parentheses - no extra spaces usually
				cOutput += cOperator
			case ","
				# Comma - space after
				cOutput += cOperator
				if oStyle[:spaceAfterComma] {
					cOutput += " "
				}
			other
				cOutput += cOperator
			}
			
		func processLiteral oToken, nIndex, aTokens
			# Process string literals
			addToken(oToken)
			
		func processNumber oToken, nIndex, aTokens
			# Process numeric literals
			addToken(oToken)
			
		func processIdentifier oToken, nIndex, aTokens
			# Process identifiers
			addToken(oToken)
			
		func processEndLine oToken, nIndex, aTokens
			# Process end of line
			cOutput += nl
			
		func processComment oToken, nIndex, aTokens
			# Process comments - preserve them
			if not isLastCharNewline() {
				cOutput += " "
			}
			cOutput += oToken[:value]
			
		func addToken oToken
			# Add token with appropriate spacing
			cValue = oToken[:value]
			
			# Add space before token if needed
			if needsSpaceBefore(oToken) {
				cOutput += " "
			}
			
			cOutput += cValue
			
		func addIndentedToken oToken
			# Add token with proper indentation
			if not isLastCharNewline() {
				cOutput += nl
			}
			cOutput += getCurrentIndent() + oToken[:value]
			
		func needsSpaceBefore oToken
			# Determine if space is needed before token
			if len(cOutput) = 0 {
				return false
			}
			
			cLastChar = right(cOutput, 1)
			if cLastChar = " " or cLastChar = nl or cLastChar = tab {
				return false
			}
			
			# Add more sophisticated spacing rules here
			return true
			
		func getCurrentIndent
			# Get current indentation string
			return copy(cIndentString, nIndentLevel)
			
		func isLastCharNewline
			# Check if last character is newline
			if len(cOutput) = 0 {
				return true
			}
			return (right(cOutput, 1) = nl)

		func convertKeyword cKeyword
			# Convert keyword based on target language style
			if oStyle = null or oLanguageConverter = null {
				return cKeyword
			}

			cLanguageStyle = ""
			if find(oStyle, :languageStyle) {
				cLanguageStyle = oStyle[:languageStyle]
			}

			cKeyword = upper(cKeyword)

			# Convert based on target style
			switch cLanguageStyle {
			case "classic"
				return convertToClassicKeyword(cKeyword)
			case "natural"
				return convertToNaturalKeyword(cKeyword)
			case "modern"
				return convertToModernKeyword(cKeyword)
			other
				return cKeyword
			}

		func convertToClassicKeyword cKeyword
			# Convert to classic Ring keywords
			switch cKeyword {
			case "PUT" case "PRINT"
				return "see"
			case "GET" case "GETSTRING"
				return "give"
			case "DEF"
				return "func"
			case "END"
				return "ok"
			case "ELSEIF"
				return "but"
			case "CASE"
				return "on"
			case "DEFAULT"
				return "other"
			other
				return lower(cKeyword)
			}

		func convertToNaturalKeyword cKeyword
			# Convert to natural modified Ring keywords
			switch cKeyword {
			case "SEE" case "PRINT"
				return "put"
			case "GIVE" case "GETSTRING"
				return "get"
			case "FUNC"
				return "def"
			case "OK" case "NEXT" case "OFF"
				return "end"
			case "BUT"
				return "elseif"
			case "ON"
				return "case"
			other
				return lower(cKeyword)
			}

		func convertToModernKeyword cKeyword
			# Convert to modern C-like Ring keywords
			switch cKeyword {
			case "SEE" case "PUT"
				return "print"
			case "GIVE" case "GET"
				return "getstring"
			case "OK" case "END" case "NEXT" case "OFF"
				return "}"
			case "BUT"
				return "elseif"
			case "ON"
				return "case:"
			case "OTHER"
				return "default:"
			other
				return lower(cKeyword)
			}
