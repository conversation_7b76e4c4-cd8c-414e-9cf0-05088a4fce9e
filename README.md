# RingFmt - Ring Code Formatter

A code formatter tool for the Ring programming language that automatically formats Ring source code according to different coding styles.

## Features

- **Multiple Formatting Styles**: Supports three different formatting styles as documented in Ring language documentation
- **Single File Processing**: Format individual Ring files
- **Batch Processing**: Format multiple files using wildcards (*.ring)
- **Preserves Comments**: Maintains all comments in the original code
- **Safe Operation**: Creates backup files before formatting (optional)
- **Command Line Interface**: Easy to use from terminal/command prompt

## Installation

### Using RingPM (Recommended)

```bash
ringpm install ringfmt
```

### Manual Installation

1. Clone or download this repository
2. Make sure you have Ring programming language installed
3. Run the formatter using: `ring main.ring`

## Usage

### Basic Usage

```bash
# Format a single file
ringfmt myfile.ring

# Format all Ring files in current directory
ringfmt *.ring
```

### Formatting Styles

```bash
# Style 1 (Default) - Ring documentation first style
ringfmt myfile.ring -style1

# Style 2 - Ring documentation second style  
ringfmt myfile.ring -style2

# Style 3 - Ring documentation third style
ringfmt myfile.ring -style3
```

### Additional Options

```bash
# Create backup before formatting
ringfmt myfile.ring -backup

# Output to different file
ringfmt myfile.ring -output formatted_file.ring

# Verbose output
ringfmt myfile.ring -verbose
```

## Formatting Styles

### Style 1 (Default)
- Standard indentation with spaces
- Consistent spacing around operators
- Standard brace placement

### Style 2
- Alternative indentation style
- Different operator spacing
- Alternative brace placement

### Style 3
- Compact formatting style
- Minimal spacing
- Compact brace placement

## Examples

### Before Formatting
```ring
if x>5{
see"Hello"
for i=1to10{
?i
}
}
```

### After Formatting (Style 1)
```ring
if x > 5 {
    see "Hello"
    for i = 1 to 10 {
        ? i
    }
}
```

## Testing

The formatter has been tested against Ring language test files to ensure compatibility and correctness.

```bash
# Run tests
ring tests/test_formatter.ring
```

## Requirements

- Ring Programming Language 1.18 or higher
- TokensLib library (included with Ring)

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

MIT License

## Author

Ring Community

## Changelog

### Version 1.0.0
- Initial release
- Support for three formatting styles
- Command line interface
- Batch file processing
- Comment preservation
