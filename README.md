# RingFmt - Ring Code Formatter

A code formatter tool for the Ring programming language that automatically formats Ring source code according to different coding styles.

## Features

- **Language Style Conversion**: Convert Ring code between three different language styles
  - **Classic Style**: Traditional Ring syntax (see/give, func...ok, if...ok)
  - **Natural Modified Style**: Alternative syntax (put/get, def...end, if...end)
  - **Modern C-like Style**: C-style syntax (print(), func{}, if(){})
- **Code Formatting**: Automatic code formatting with proper indentation and spacing
- **Single File Processing**: Format individual Ring files
- **Batch Processing**: Format multiple files using wildcards (*.ring)
- **Preserves Comments**: Maintains all comments in the original code
- **Safe Operation**: Creates backup files before formatting (optional)
- **Command Line Interface**: Easy to use from terminal/command prompt

## Installation

### Using RingPM (Recommended)

```bash
ringpm install ringfmt
```

### Manual Installation

1. Clone or download this repository
2. Make sure you have Ring programming language installed
3. Run the formatter using: `ring main.ring`

## Usage

### Basic Usage

```bash
# Format a single file
ringfmt myfile.ring

# Format all Ring files in current directory
ringfmt *.ring
```

### Language Style Conversion

```bash
# Style 1 (Default) - Classic Ring style (see/give, func...ok)
ringfmt myfile.ring -style1
ringfmt myfile.ring -classic

# Style 2 - Natural Modified style (put/get, def...end)
ringfmt myfile.ring -style2
ringfmt myfile.ring -natural

# Style 3 - Modern C-like style (print(), func{})
ringfmt myfile.ring -style3
ringfmt myfile.ring -modern
```

### Additional Options

```bash
# Create backup before formatting
ringfmt myfile.ring -backup

# Output to different file
ringfmt myfile.ring -output formatted_file.ring

# Verbose output
ringfmt myfile.ring -verbose
```

## Language Styles

### Style 1 - Classic Ring (Default)
- **Output**: `see "Hello"` or `? "Hello"`
- **Input**: `give cName`
- **Functions**: `func name ... ok`
- **Conditionals**: `if condition ... but ... else ... ok`
- **Loops**: `for i = 1 to 10 ... next`
- **Switch**: `on value ... on case ... other ... off`

### Style 2 - Natural Modified
- **Output**: `put "Hello"`
- **Input**: `get cName`
- **Functions**: `def name ... end`
- **Conditionals**: `if condition ... elseif ... else ... end`
- **Loops**: `for i = 1 to 10 ... end`
- **Switch**: `case value ... case ... other ... end`
- **Error Handling**: `try ... catch ... end`

### Style 3 - Modern C-like
- **Output**: `print("Hello")` or `puts("Hello")`
- **Input**: `getstring()`
- **Functions**: `func name() { ... }`
- **Conditionals**: `if (condition) { ... } elseif { ... } else { ... }`
- **Loops**: `for (i = 1; i <= 10; i++) { ... }`
- **Switch**: `switch (value) { case: ... default: ... }`
- **Error Handling**: `try { ... } catch { ... }`

## Examples

### Language Style Conversion Examples

#### Original Code (Classic Style)
```ring
see "Enter your name: "
give cName
func greet cPersonName
    see "Hello " + cPersonName + nl
    return true
ok
if len(cName) > 0
    greet(cName)
else
    see "No name provided" + nl
ok
```

#### Converted to Natural Modified Style
```ring
put "Enter your name: "
get cName
def greet cPersonName
    put "Hello " + cPersonName
    return true
end
if len(cName) > 0
    greet(cName)
else
    put "No name provided"
end
```

#### Converted to Modern C-like Style
```ring
print("Enter your name: ")
cName = getstring()
func greet(cPersonName) {
    print("Hello " + cPersonName + "\n")
    return true
}
if (len(cName) > 0) {
    greet(cName)
} else {
    print("No name provided\n")
}
```

## Testing

The formatter has been tested against Ring language test files to ensure compatibility and correctness.

```bash
# Run language style conversion tests
ring tests/test_language_styles.ring

# Test with sample files
ringfmt tests/samples/classic_style.ring -natural -verbose
ringfmt tests/samples/natural_style.ring -modern -verbose
ringfmt tests/samples/modern_style.ring -classic -verbose
```

## Requirements

- Ring Programming Language 1.18 or higher
- TokensLib library (included with Ring)

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

MIT License

## Author

Ring Community

## Changelog

### Version 1.1.0
- **Language Style Conversion**: Convert between Classic, Natural Modified, and Modern C-like styles
- **Enhanced CLI**: New command line options for language styles (-classic, -natural, -modern)
- **Sample Files**: Example files for each language style
- **Comprehensive Testing**: Test suite for language style conversion

### Version 1.0.0
- Initial release
- Support for three formatting styles
- Command line interface
- Batch file processing
- Comment preservation
