# Modern C-like Ring Style Example (Style 3)
# Uses: print()/puts(), func{}, if(){}

# Simple output
print("Hello World!\n")
puts("This is modern C-like Ring style")

# Input and variables
print("Enter your name: ")
cName = getstring()
print("Hello " + cName + "\n")

# Function call
nSum = calculateSum(10, 20)
print("Sum: #{nSum}\n")

# Conditional statements
if (nSum > 25) {
    print("Sum is greater than 25\n")
elseif (nSum == 25)
    print("Sum equals 25\n")
else 
    print("Sum is less than 25\n")
}

# Loop example
for (i = 1; i <= 5; i++) {
    print("Number: #{i}\n")
}

# While loop
nCounter = 1
while (nCounter <= 3) {
    print("Counter: #{nCounter}\n")
    nCounter++
}

# Switch statement
nValue = 2
switch (nValue) {
    case: 1
        print("Value is one\n")
    case: 2
        print("Value is two\n")
    default:
        print("Value is something else\n")
}

# Error handling
try {
    nResult = 10 / 0
catch
    print("Error occurred: " + cCatchError + "\n")
}


# Create object and use it
oPerson = new Person("<PERSON>", 28)
oPerson.introduce()

# Array example
aNumbers = [1, 2, 3, 4, 5]
for (nNum in aNumbers) {
    print("Array item: #{nNum}\n")
}

# List operations
aList = []
add(aList, "Apple")
add(aList, "Banana")
add(aList, "Orange")

for (cFruit in aList) {
    print("Fruit: #{cFruit}\n")
}

print("Modern C-like Ring style example completed!\n")

# Function definition
func calculateSum(nX, nY) {
    nResult = nX + nY
    return nResult
}

# Class definition
class Person {
    cName = ""
    nAge = 0
    
    func init(cPersonName, nPersonAge) {
        cName = cPersonName
        nAge = nPersonAge
    }
    
    func introduce() {
        print("My name is #{cName} and I am #{nAge} years old\n")
    }
    
    private
    
    func privateMethod() {
        print("This is a private method\n")
    }
}
