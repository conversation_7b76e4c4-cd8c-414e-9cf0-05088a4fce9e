#!/usr/bin/env ring

/*
**	Test Language Style Conversion
**	Tests the conversion between different Ring language styles
*/

load "../ringfmt.ring"

func main
    ? "Testing Ring Language Style Conversion"
    ? "======================================"
    
    # Test basic conversion
    testBasicConversion()
    
    # Test file conversion
    testFileConversion()
    
    ? ""
    ? "All tests completed!"

func testBasicConversion
    ? ""
    ? "Testing Basic Conversion..."
    
    # Create formatter instance
    oRingFmt = new RingFmt
    oRingFmt.setVerbose(true)
    
    # Test code snippet
    cTestCode = '
see "Hello World!" + nl
give cName
func test
    return "OK"
ok
if x > 5
    ? "Greater"
but x = 5
    ? "Equal"
else
    ? "Less"
ok
'
    
    ? "Original Code (Classic Style):"
    ? cTestCode
    
    # Convert to Natural style
    ? ""
    ? "Converting to Natural Style..."
    cNaturalCode = oRingFmt.formatString(cTestCode, "style2")
    ? "Natural Style Result:"
    ? cNaturalCode
    
    # Convert to Modern style
    ? ""
    ? "Converting to Modern Style..."
    cModernCode = oRingFmt.formatString(cTestCode, "style3")
    ? "Modern Style Result:"
    ? cModernCode

func testFileConversion
    ? ""
    ? "Testing File Conversion..."
    
    # Create test input file
    cTestFile = "test_input.ring"
    cTestContent = '
# Test file for conversion
see "Testing file conversion" + nl

func greet cName
    see "Hello " + cName + nl
    return true
ok

if len(cName) > 0
    greet(cName)
else
    see "No name provided" + nl
ok

for i = 1 to 3
    ? "Count: " + i
next
'
    
    write(cTestFile, cTestContent)
    
    # Test conversion to each style
    testStyleConversion(cTestFile, "style1", "classic")
    testStyleConversion(cTestFile, "style2", "natural")  
    testStyleConversion(cTestFile, "style3", "modern")
    
    # Clean up
    if fexists(cTestFile) {
        remove(cTestFile)
    }

func testStyleConversion cInputFile, cStyle, cStyleName
    ? ""
    ? "Converting to " + cStyleName + " style (" + cStyle + ")..."
    
    cOutputFile = "test_output_" + cStyle + ".ring"
    
    try {
        oRingFmt = new RingFmt
        oRingFmt.setVerbose(false)
        oRingFmt.formatFile(cInputFile, cOutputFile, cStyle)
        
        if fexists(cOutputFile) {
            ? "Conversion successful! Output saved to: " + cOutputFile
            
            # Show first few lines of result
            cResult = read(cOutputFile)
            aLines = str2list(cResult)
            ? "First 10 lines of result:"
            for i = 1 to min(10, len(aLines)) {
                ? "  " + aLines[i]
            }
            
            # Clean up output file
            remove(cOutputFile)
        else
            ? "Error: Output file not created"
        }
        
    catch
        ? "Error during conversion: " + cCatchError
    }

func min nA, nB
    if nA < nB {
        return nA
    }
    return nB
