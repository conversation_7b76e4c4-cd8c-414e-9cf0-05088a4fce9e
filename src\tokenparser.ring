/*
**	TokenParser Class
**	Parses Ring source code into tokens using tokenslib
**	Supports comments and provides enhanced token information
*/

load "tokenslib.ring"

class TokenParser

	# Properties
	aTokens = []
	aEnhancedTokens = []
	cSourceCode = ""
	
	# Token type constants (from tokenslib)
	C_KEYWORD 	= 0
	C_OPERATOR 	= 1
	C_LITERAL 	= 2
	C_NUMBER 	= 3
	C_IDENTIFIER 	= 4
	C_ENDLINE 	= 5
	C_COMMENT	= 6
	
	func parseFile cFileName
		# Parse tokens from file
		if not fexists(cFileName) {
			raise("File does not exist: " + cFileName)
		}
		
		cSourceCode = read(cFileName)
		return parseString(cSourceCode)
		
	func parseString cCode
		# Parse tokens from string including comments
		cSourceCode = cCode
		
		# Use ring_state_stringtokens to get all tokens including comments
		pState = ring_state_new()
		aTokens = ring_state_stringtokens(pState, cCode, false, true)
		ring_state_delete(pState)
		
		# Enhance tokens with additional information
		enhanceTokens()
		
		return aEnhancedTokens
		
	func getTokens
		return aEnhancedTokens
		
	func getSourceCode
		return cSourceCode
		
	func printTokens
		# Debug function to print all tokens
		? "=== TOKENS ==="
		for i = 1 to len(aEnhancedTokens) {
			aToken = aEnhancedTokens[i]
			? "Token " + i + ": " + getTokenTypeName(aToken[:type]) + 
			  " = '" + aToken[:value] + "'"
		}
		? "=============="
		
	private
	
		func enhanceTokens
			# Convert raw tokens to enhanced format with additional info
			aEnhancedTokens = []
			
			for aToken in aTokens {
				oEnhancedToken = [
					:type = aToken[1],
					:value = aToken[2], 
					:index = aToken[3],
					:line = 0,		# Will be calculated
					:column = 0,	# Will be calculated
					:isKeyword = (aToken[1] = C_KEYWORD),
					:isOperator = (aToken[1] = C_OPERATOR),
					:isLiteral = (aToken[1] = C_LITERAL),
					:isNumber = (aToken[1] = C_NUMBER),
					:isIdentifier = (aToken[1] = C_IDENTIFIER),
					:isEndLine = (aToken[1] = C_ENDLINE),
					:isComment = (aToken[1] = C_COMMENT)
				]
				
				# Add keyword name if it's a keyword
				if oEnhancedToken[:isKeyword] {
					oEnhancedToken[:keywordName] = getKeywordName(aToken[3])
				}
				
				add(aEnhancedTokens, oEnhancedToken)
			}
			
			# Calculate line and column positions
			calculatePositions()
			
		func calculatePositions
			# Calculate line and column for each token
			nLine = 1
			nColumn = 1
			
			for oToken in aEnhancedTokens {
				oToken[:line] = nLine
				oToken[:column] = nColumn
				
				if oToken[:isEndLine] {
					nLine++
					nColumn = 1
				else
					nColumn += len(oToken[:value])
				}
			}
			
		func getTokenTypeName nType
			# Get human readable token type name
			switch nType {
			case C_KEYWORD
				return "KEYWORD"
			case C_OPERATOR
				return "OPERATOR"
			case C_LITERAL
				return "LITERAL"
			case C_NUMBER
				return "NUMBER"
			case C_IDENTIFIER
				return "IDENTIFIER"
			case C_ENDLINE
				return "ENDLINE"
			case C_COMMENT
				return "COMMENT"
			other
				return "UNKNOWN"
			}
			
		func getKeywordName nIndex
			# Get keyword name from index
			aKeywords = [
				"IF","TO","OR","AND","NOT","FOR","FOREACH","NEW","FUNC", 
				"FROM","NEXT","LOAD","ELSE","SEE","WHILE","OK",
				"CLASS","RETURN","BUT", 
				"END","GIVE","BYE","EXIT","TRY","CATCH","DONE",
				"SWITCH","ON","OTHER","OFF", 
				"IN","LOOP","PACKAGE","IMPORT","PRIVATE","STEP","DO",
				"AGAIN","CALL","ELSEIF", 
				"PUT","GET","CASE","DEF","ENDFUNC","ENDCLASS","ENDPACKAGE",
				"ENDIF","ENDFOR","ENDWHILE","ENDSWITCH","ENDTRY",
				"FUNCTION","ENDFUNCTION","BREAK","CONTINUE", 
				"CHANGERINGKEYWORD","CHANGERINGOPERATOR","LOADSYNTAX"
			]
			
			if nIndex >= 1 and nIndex <= len(aKeywords) {
				return aKeywords[nIndex]
			}
			return "UNKNOWN_KEYWORD"
